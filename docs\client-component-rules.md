# Client Component Rules for Next.js 15

## 🚨 Critical Rule: "use client" Directive

**ALWAYS add `"use client"` as the FIRST LINE** of any component file that uses:

### ✅ Requires "use client"

#### React Hooks
- `useState` - Managing component state
- `useEffect` - Side effects and lifecycle
- `useCallback` - Memoizing functions
- `useMemo` - Memoizing values
- `useRef` - DOM references
- `useContext` - Context consumption
- `useReducer` - Complex state management
- `useLayoutEffect` - DOM measurements
- Custom hooks that use any of the above

#### Event Handlers
- `onClick` - Click events
- `onChange` - Input changes
- `onSubmit` - Form submissions
- `onFocus` / `onBlur` - Focus events
- `onMouseOver` / `onMouseOut` - Mouse events
- `onKeyDown` / `onKeyUp` - Keyboard events
- Any event handler function

#### Browser APIs
- `localStorage` - Local storage access
- `sessionStorage` - Session storage access
- `window` - Window object
- `document` - Document object
- `navigator` - Navigator object
- `fetch` - API calls (client-side)
- `setTimeout` / `setInterval` - Timers
- `addEventListener` - Event listeners

#### Next.js Client Hooks
- `useRouter` - Client-side routing
- `usePathname` - Current pathname
- `useSearchParams` - URL search parameters
- `useParams` - Dynamic route parameters

### ❌ Does NOT require "use client"

#### Server Components (Default)
- Static content rendering
- Props-only components
- Server-side data fetching
- Components that only render JSX
- Utility functions without hooks

## 📝 Correct Format

```tsx
"use client"

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'

export function MyComponent() {
  const [count, setCount] = useState(0)
  
  return (
    <Button onClick={() => setCount(count + 1)}>
      Count: {count}
    </Button>
  )
}
```

## ❌ Common Mistakes

### Missing "use client"
```tsx
// ❌ WRONG - Will cause error
import React, { useState } from 'react'

export function MyComponent() {
  const [count, setCount] = useState(0) // Error: useState only works in client components
  return <div>{count}</div>
}
```

### Incorrect placement
```tsx
// ❌ WRONG - "use client" must be first line
import React from 'react'
"use client"

export function MyComponent() {
  // ...
}
```

### Missing quotes
```tsx
// ❌ WRONG - Must be quoted string
use client

import React from 'react'
// ...
```

## 🔍 Quick Decision Tree

**Ask yourself:**
1. Does my component use any React hooks? → **Yes** = Add "use client"
2. Does my component handle user interactions? → **Yes** = Add "use client"
3. Does my component access browser APIs? → **Yes** = Add "use client"
4. Does my component only render static content? → **Yes** = Server component (no "use client")

**When in doubt:** Add "use client" - it's safer than runtime errors!

## 🛠️ Common Patterns

### Interactive Form
```tsx
"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export function ContactForm() {
  const [email, setEmail] = useState('')
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
  }
  
  return (
    <form onSubmit={handleSubmit}>
      <Input 
        value={email} 
        onChange={(e) => setEmail(e.target.value)} 
      />
      <Button type="submit">Submit</Button>
    </form>
  )
}
```

### Static Display Component
```tsx
// No "use client" needed - server component
interface Props {
  title: string
  description: string
}

export function InfoCard({ title, description }: Props) {
  return (
    <div className="p-4 border rounded">
      <h2 className="text-xl font-bold">{title}</h2>
      <p className="text-gray-600">{description}</p>
    </div>
  )
}
```

### Data Fetching with State
```tsx
"use client"

import { useState, useEffect } from 'react'

export function UserProfile() {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    // Fetch user data
    fetchUser().then(setUser).finally(() => setLoading(false))
  }, [])
  
  if (loading) return <div>Loading...</div>
  
  return <div>Welcome, {user?.name}</div>
}
```

## 🚀 Best Practices

1. **Start with "use client"** if you're unsure
2. **Check your imports** - if you import hooks, you need "use client"
3. **Review event handlers** - any onClick, onChange, etc. needs "use client"
4. **Consider the component's purpose** - interactive = client, static = server
5. **Test your components** - missing "use client" will cause runtime errors
6. **Use TypeScript** - it helps catch these issues early

## 🔧 Debugging Tips

If you see errors like:
- "You're importing a component that needs useState"
- "This React hook only works in a client component"
- "useEffect only works in Client Components"

**Solution:** Add `"use client"` as the first line of your component file.

Remember: It's better to have an unnecessary "use client" than a broken component!
