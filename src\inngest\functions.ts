import { inngest } from "./client";
import { Sandbox } from "@e2b/code-interpreter";
import { openai, createAgent, createTool, createNetwork, type Tool, type Message, createState, anthropic } from "@inngest/agent-kit";
import { getSandbox, lastAssistantTextMessageContent, parseAgentOutput, estimateTokenCount, createChunkedResponse, isResponseTooLarge } from "./utils";
import { z } from "zod";
import { FRAGMENT_TITLE_PROMPT, PROMPT, RESPONSE_PROMPT } from "@/prompt";
import { prisma } from "@/lib/db";
import { SANDBOX_TIMEOUT } from "./type";
import { selectOptimalModel, createOptimalModel, calculateOptimalTokens, supportsSystemMessages } from "./model-config";
import { StreamingResponseHandler, calculateResponseCost } from "./streaming-handler";
import { ResponseContinuationSystem } from "./continuation-system";
import { ResponseSizeAnalyzer } from "./response-analyzer";

interface AgentState {
  summary: string;
  files: {[path: string]: string };
};


export const codeAgentFunction = inngest.createFunction(
  { id: "code-agent" },
  { event: "code-agent/run" },
  async ({ event, step }) => {
    const sandboxId = await step.run("get-sandbox-id", async () => {
      const sandbox = await Sandbox.create("vibe-nextjs-test3");
      await sandbox.setTimeout(SANDBOX_TIMEOUT);
      return sandbox.sandboxId;
    });

    const previousMessages = await step.run("get-previous-messages", async() => {
      const formattedMessages: Message[] = [];

      const messages = await prisma.message.findMany({
        where: {
          projectId: event.data.projectId,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 5,
      });

      for (const message of messages) {
        formattedMessages.push({
          type: "text",
          role: message.role === "ASSISTANT" ? "assistant" : "user",
          content: message.content,
        });
      }

      return formattedMessages.reverse();
    })

    const state = createState<AgentState>(
      {
        summary: "",
        files: {},
    },
    {
      messages: previousMessages,
    },
  );

    // Analyze prompt and predict response size
    const promptTokens = estimateTokenCount(event.data.value + PROMPT);
    const optimalTokens = calculateOptimalTokens(promptTokens, 'mixed', 'moderate');
    const modelConfig = selectOptimalModel(optimalTokens, 'moderate', true); // Prioritize quality for Next.js generation

    // Use response analyzer for better prediction
    const responseAnalyzer = new ResponseSizeAnalyzer(modelConfig);
    const responsePrediction = responseAnalyzer.analyzePrompt(event.data.value);
    const optimalStrategy = responseAnalyzer.getOptimalStrategy(responsePrediction);

    console.log(`Selected model: ${modelConfig.name} with ${modelConfig.maxTokens} max tokens`);
    console.log(`Response prediction:`, responsePrediction);
    console.log(`Optimal strategy:`, optimalStrategy.strategy);

    // Handle o1 models that don't support system messages
    const agentConfig: any = {
      name: "code-agent",
      description: "Coding Agent",
      model: createOptimalModel(modelConfig, 0.5),
      tools: [
        createTool({
          name: "terminal",
          description: "Use the terminal to run commands",
          parameters: z.object({
            command: z.string(),
          }),
          handler: async ({ command }, { step }) => {
            return await step?.run("terminal", async () => {
              const buffers = { stdout: "", stderr: "" };

              try {
                const sandbox = await getSandbox(sandboxId);
                const result = await sandbox.commands.run(command, {
                  onStdout: (data: string) => {
                    buffers.stdout += data;
                  },
                  onStderr: (data: string) => {
                    buffers.stderr += data;
                  }
                });
                return result.stdout;
              } catch (e) {
                console.error(
                  `Command failed: ${e} \nstdout: ${buffers.stdout}\nstderror: ${buffers.stderr}`,);
                return `Command failed: ${e} \nstdout: ${buffers.stdout}\nstderror: ${buffers.stderr}`;
              }

            });
          },
        }),
        createTool({
          name: "createOrUpdateFiles",
          description: "Create or Update Files",
          parameters: z.object({
            files: z.array(
            z.object({
              path: z.string(),
              content: z.string(),
            })
          ),
        }),
        handler: async (
          { files },
          { step, network }: Tool.Options<AgentState>
        ) => {
          const newFiles = await step?.run("createOrUpdateFiles", async () => {
            try {
              const updatedFiles = network.state.data.files || {};
              const sandbox = await getSandbox(sandboxId);
              for (const file of files) {
                await sandbox.files.write(file.path, file.content);
                updatedFiles[file.path] = file.content;
              }
              return updatedFiles;
            } catch (e) {
              return "Error: " + e;
            }
          });

          if (typeof newFiles === "object") {
            network.state.data.files = newFiles;
          }
        },
  }),
  createTool({
    name: "readFiles",
    description: "Read files from the sandbox",
    parameters: z.object({
      files: z.array(z.string()),
    }),
    handler: async ({ files }, { step }) => {
      return await step?.run("readFiles", async () => {
        try {
          const sandbox = await getSandbox(sandboxId);
          const contents = [];
          for (const file of files) {
            const content = await sandbox.files.read(file);
            contents.push({ path: file, content });
          }
          return JSON.stringify(contents);
        } catch (e) {
          return "Error: " + e;
        }
        });
      }
    }),
  ],
  lifecycle: {
    onResponse: async ({ result, network }) => {
      const lastAssistantMessageText = lastAssistantTextMessageContent(result);
      if (lastAssistantMessageText && network) {
        if (lastAssistantMessageText.includes("<task_summary>")){
          network.state.data.summary = lastAssistantMessageText
        }

        // Check if response was truncated and needs continuation
        const estimatedTokens = estimateTokenCount(lastAssistantMessageText);
        const continuationSystem = new ResponseContinuationSystem(modelConfig);

        if (continuationSystem.isTruncated(lastAssistantMessageText)) {
          console.log(`Response truncated (${estimatedTokens} tokens). Triggering continuation system.`);

          // Trigger continuation in background
          await inngest.send({
            name: "response/continue",
            data: {
              projectId: event.data.projectId,
              parentMessageId: "will-be-set-after-save", // This would need to be handled properly
              modelConfig
            }
          });
        }
      }
      return result;

    },
  },
  };

  // Only add system prompt for models that support it
  if (supportsSystemMessages(modelConfig)) {
    agentConfig.system = PROMPT;
  }

  const codeAgent = createAgent<AgentState>(agentConfig);

  const network = createNetwork<AgentState>({
    name: "coding-agent-network",
    agents: [codeAgent],
    maxIter: 15,
    defaultState: state,
    router: async ({ network }) => {
      const summary = network.state.data.summary;
      if (summary) {
        return;
      }
      return codeAgent;
    },
  });
  // For o1 models, prepend system prompt to user message since they don't support system messages
  const userMessage = supportsSystemMessages(modelConfig)
    ? event.data.value
    : `${PROMPT}\n\nUser Request: ${event.data.value}`;

  const result = await network.run(userMessage, {state});

  const fragmentTitleGenerator = createAgent({
      name: "fragment-title-generator",
      description: "A fragment title generator",
      system: FRAGMENT_TITLE_PROMPT,
      model: openai({ 
        model: "gpt-4o",}),
      });
  const responseGenerator = createAgent({
      name: "response-generator",
      description: "A response generator",
      system: RESPONSE_PROMPT,
      model: openai({ 
        model: "gpt-4o",}),
      });
      const { output: fragmentTitleOutput } = await fragmentTitleGenerator.run(result.state.data.summary);
      const { output: responseOutput } = await responseGenerator.run(result.state.data.summary);


  const isError = !result.state.data.summary || Object.keys(result.state.data.files || {}).length === 0;

      const sandboxUrl = await step.run("get-sandbox-url", async () => {
        const sandbox = await getSandbox(sandboxId);
        const host = sandbox.getHost(3000);
        return `https://${host}`;
      });

      await step.run("save-result", async () =>{
        if (isError){
          return await prisma.message.create({
            data:{
              projectId: event.data.projectId,
              content: "Something Went Wrong. Please try again.",
              role: "ASSISTANT",
              type: "ERROR",
            },
          });
        }

        const responseContent = parseAgentOutput(responseOutput);
        const estimatedTokens = estimateTokenCount(responseContent);

        // Calculate cost and strategy
        const costAnalysis = calculateResponseCost(estimatedTokens, modelConfig, 'single');
        console.log(`Response cost analysis:`, costAnalysis);

        // Use streaming handler for large responses
        if (estimatedTokens > modelConfig.maxTokens * 0.8) {
          const streamingHandler = new StreamingResponseHandler({
            projectId: event.data.projectId,
            modelConfig,
            maxChunkSize: Math.floor(modelConfig.maxTokens * 0.8)
          });

          const streamingResult = await streamingHandler.handleLargeResponse(responseContent);
          console.log(`Streaming result:`, streamingResult);

          // Create fragment for the first chunk/main response
          if (streamingResult.chunks > 0) {
            return await prisma.message.create({
              data:{
                projectId: event.data.projectId,
                content: responseContent.slice(0, Math.floor(responseContent.length / streamingResult.chunks)),
                role: "ASSISTANT",
                type: streamingResult.strategy === 'single' ? "RESULT" : "STREAMING_CHUNK",
                metadata: {
                  strategy: streamingResult.strategy,
                  totalChunks: streamingResult.chunks,
                  totalTokens: streamingResult.totalTokens,
                  modelUsed: modelConfig.name,
                  costAnalysis
                },
                fragment: {
                  create: {
                    sandboxUrl: sandboxUrl,
                    title: parseAgentOutput(fragmentTitleOutput),
                    files: result.state.data.files,
                  },
                },
              },
            });
          }
        }

        // Standard single response
        return await prisma.message.create({
          data:{
            projectId: event.data.projectId,
            content: responseContent,
            role: "ASSISTANT",
            type: "RESULT",
            metadata: {
              strategy: 'single',
              totalTokens: estimatedTokens,
              modelUsed: modelConfig.name,
              costAnalysis
            },
            fragment: {
              create: {
                sandboxUrl: sandboxUrl,
                title: parseAgentOutput(fragmentTitleOutput),
                files: result.state.data.files,
              },
            },
          },
        })
      });

      return { 
        url: sandboxUrl,
        title: "Fragment",
        files: result.state.data.files,
        summary: result.state.data.summary,
      };
    },
);
