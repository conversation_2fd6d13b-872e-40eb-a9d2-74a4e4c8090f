import { inngest } from "./client";
import { createAgent } from "@inngest/agent-kit";
import { prisma } from "@/lib/db";
import { estimateTokenCount, lastAssistantTextMessageContent } from "./utils";
import { ModelConfig, createOptimalModel } from "./model-config";

// Continuation system for handling responses that exceed token limits
export interface ContinuationRequest {
  projectId: string;
  parentMessageId: string;
  previousContent: string;
  remainingPrompt: string;
  modelConfig: ModelConfig;
  maxContinuations: number;
}

export class ResponseContinuationSystem {
  private modelConfig: ModelConfig;
  private maxContinuations: number;

  constructor(modelConfig: ModelConfig, maxContinuations: number = 5) {
    this.modelConfig = modelConfig;
    this.maxContinuations = maxContinuations;
  }

  // Check if a response was likely truncated
  isTruncated(content: string): boolean {
    const tokens = estimateTokenCount(content);
    const threshold = this.modelConfig.maxTokens * 0.95; // 95% of max tokens
    
    // Also check for incomplete sentences or code blocks
    const endsIncomplete = !content.match(/[.!?]$/) || 
                          content.includes('```') && (content.split('```').length % 2 === 0);
    
    return tokens >= threshold || endsIncomplete;
  }

  // Generate continuation prompt
  generateContinuationPrompt(previousContent: string, context: string): string {
    const lastPart = previousContent.slice(-1000); // Last 1000 chars for context
    
    return `Continue the previous response. You were in the middle of providing a detailed response.

Previous context (last part):
${lastPart}

Continue from exactly where you left off. Do not repeat any of the previous content. Maintain the same tone, style, and technical level. If you were in the middle of code, continue the code. If you were explaining something, continue the explanation.

Continue:`;
  }

  // Create continuation agent
  createContinuationAgent(systemPrompt: string) {
    return createAgent({
      name: "continuation-agent",
      description: "Agent for continuing truncated responses",
      system: systemPrompt,
      model: createOptimalModel(this.modelConfig, 0.3), // Lower temperature for consistency
    });
  }

  // Process continuation request
  async processContinuation(request: ContinuationRequest): Promise<{
    success: boolean;
    continuationId?: string;
    totalParts: number;
    isComplete: boolean;
    error?: string;
  }> {
    try {
      const continuationPrompt = this.generateContinuationPrompt(
        request.previousContent,
        request.remainingPrompt
      );

      const continuationAgent = this.createContinuationAgent(
        "You are a continuation agent. Your job is to seamlessly continue responses that were cut off due to token limits."
      );

      const result = await continuationAgent.run(continuationPrompt);
      const continuationContent = lastAssistantTextMessageContent(result);

      if (!continuationContent) {
        return {
          success: false,
          totalParts: 1,
          isComplete: false,
          error: "No continuation content generated"
        };
      }

      // Save continuation as a child message
      const continuationMessage = await prisma.message.create({
        data: {
          projectId: request.projectId,
          content: continuationContent,
          role: "ASSISTANT",
          type: "STREAMING_CHUNK",
          parentMessageId: request.parentMessageId,
          metadata: {
            isContinuation: true,
            modelUsed: this.modelConfig.name,
            tokens: estimateTokenCount(continuationContent)
          }
        }
      });

      // Check if this continuation is also truncated
      const needsMoreContinuation = this.isTruncated(continuationContent);
      
      if (needsMoreContinuation && request.maxContinuations > 1) {
        // Recursively continue
        const nextRequest: ContinuationRequest = {
          ...request,
          parentMessageId: continuationMessage.id,
          previousContent: request.previousContent + " " + continuationContent,
          maxContinuations: request.maxContinuations - 1
        };

        const nextResult = await this.processContinuation(nextRequest);
        return {
          success: true,
          continuationId: continuationMessage.id,
          totalParts: 1 + nextResult.totalParts,
          isComplete: nextResult.isComplete,
        };
      }

      return {
        success: true,
        continuationId: continuationMessage.id,
        totalParts: 1,
        isComplete: !needsMoreContinuation
      };

    } catch (error) {
      console.error("Continuation processing error:", error);
      return {
        success: false,
        totalParts: 0,
        isComplete: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  // Get complete response by combining all parts
  async getCombinedResponse(parentMessageId: string): Promise<{
    content: string;
    totalParts: number;
    totalTokens: number;
  }> {
    // Get parent message
    const parentMessage = await prisma.message.findUnique({
      where: { id: parentMessageId },
      include: {
        childMessages: {
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    if (!parentMessage) {
      throw new Error("Parent message not found");
    }

    const allParts = [parentMessage.content];
    allParts.push(...parentMessage.childMessages.map(child => child.content));

    const combinedContent = allParts.join(" ");
    
    return {
      content: combinedContent,
      totalParts: allParts.length,
      totalTokens: estimateTokenCount(combinedContent)
    };
  }
}

// Inngest function for handling continuation requests
export const continuationFunction = inngest.createFunction(
  { id: "response-continuation" },
  { event: "response/continue" },
  async ({ event, step }) => {
    const { projectId, parentMessageId, modelConfig } = event.data;

    return await step.run("process-continuation", async () => {
      // Get the parent message
      const parentMessage = await prisma.message.findUnique({
        where: { id: parentMessageId }
      });

      if (!parentMessage) {
        throw new Error("Parent message not found");
      }

      const continuationSystem = new ResponseContinuationSystem(modelConfig);
      
      if (!continuationSystem.isTruncated(parentMessage.content)) {
        return { message: "Response is not truncated, no continuation needed" };
      }

      const continuationRequest: ContinuationRequest = {
        projectId,
        parentMessageId,
        previousContent: parentMessage.content,
        remainingPrompt: "", // Could be extracted from original request
        modelConfig,
        maxContinuations: 3
      };

      const result = await continuationSystem.processContinuation(continuationRequest);
      
      if (result.success) {
        // Update parent message metadata
        await prisma.message.update({
          where: { id: parentMessageId },
          data: {
            metadata: {
              ...parentMessage.metadata as any,
              hasContinuations: true,
              totalParts: result.totalParts,
              isComplete: result.isComplete
            }
          }
        });
      }

      return result;
    });
  }
);
