import { prisma } from "@/lib/db";
import { createChunkedResponse, estimateTokenCount } from "./utils";
import { ModelConfig } from "./model-config";

// Streaming response handler for large outputs
export interface StreamingConfig {
  projectId: string;
  modelConfig: ModelConfig;
  maxChunkSize: number;
  onChunkComplete?: (chunk: string, index: number, total: number) => Promise<void>;
}

export class StreamingResponseHandler {
  private config: StreamingConfig;
  private chunks: string[] = [];
  private currentChunk = "";
  private isStreaming = false;

  constructor(config: StreamingConfig) {
    this.config = config;
  }

  async startStreaming(initialContent: string): Promise<void> {
    this.isStreaming = true;
    
    // Check if content needs chunking
    if (estimateTokenCount(initialContent) > this.config.modelConfig.maxTokens) {
      const chunkedResponse = createChunkedResponse(initialContent, this.config.maxChunkSize);
      this.chunks = chunkedResponse.chunks;
      
      // Process chunks sequentially
      for (let i = 0; i < this.chunks.length; i++) {
        await this.processChunk(this.chunks[i], i, this.chunks.length);
      }
    } else {
      // Single response, no chunking needed
      await this.processChunk(initialContent, 0, 1);
    }
    
    this.isStreaming = false;
  }

  private async processChunk(chunk: string, index: number, total: number): Promise<void> {
    // Save chunk to database with streaming metadata
    await prisma.message.create({
      data: {
        projectId: this.config.projectId,
        content: chunk,
        role: "ASSISTANT",
        type: total > 1 ? "STREAMING_CHUNK" : "RESULT",
        metadata: {
          chunkIndex: index,
          totalChunks: total,
          isStreaming: this.isStreaming,
          modelUsed: this.config.modelConfig.name
        }
      }
    });

    // Call optional callback
    if (this.config.onChunkComplete) {
      await this.config.onChunkComplete(chunk, index, total);
    }
  }

  async handleLargeResponse(content: string): Promise<{
    success: boolean;
    chunks: number;
    totalTokens: number;
    strategy: 'single' | 'chunked' | 'streaming';
  }> {
    const estimatedTokens = estimateTokenCount(content);
    
    if (estimatedTokens <= this.config.modelConfig.maxTokens * 0.8) {
      // Single response
      await this.processChunk(content, 0, 1);
      return {
        success: true,
        chunks: 1,
        totalTokens: estimatedTokens,
        strategy: 'single'
      };
    } else if (estimatedTokens <= this.config.modelConfig.maxTokens * 3) {
      // Chunked response
      await this.startStreaming(content);
      return {
        success: true,
        chunks: this.chunks.length,
        totalTokens: estimatedTokens,
        strategy: 'chunked'
      };
    } else {
      // Very large response - use streaming with continuation
      await this.startStreaming(content);
      return {
        success: true,
        chunks: this.chunks.length,
        totalTokens: estimatedTokens,
        strategy: 'streaming'
      };
    }
  }
}

// Progressive response builder for handling continuation
export class ProgressiveResponseBuilder {
  private parts: string[] = [];
  private maxTokensPerPart: number;
  private modelConfig: ModelConfig;

  constructor(modelConfig: ModelConfig, maxTokensPerPart?: number) {
    this.modelConfig = modelConfig;
    this.maxTokensPerPart = maxTokensPerPart || Math.floor(modelConfig.maxTokens * 0.8);
  }

  addPart(content: string): void {
    this.parts.push(content);
  }

  needsContinuation(): boolean {
    const totalTokens = estimateTokenCount(this.parts.join(" "));
    return totalTokens > this.maxTokensPerPart;
  }

  getNextPrompt(): string {
    const context = this.parts.slice(-2).join(" "); // Last 2 parts for context
    return `Continue the previous response. Maintain context and continue from where you left off.

Previous context: ${context.slice(-500)}...

Continue the response:`;
  }

  getCombinedResponse(): string {
    return this.parts.join(" ");
  }

  getStats(): {
    parts: number;
    totalTokens: number;
    averageTokensPerPart: number;
  } {
    const totalTokens = estimateTokenCount(this.getCombinedResponse());
    return {
      parts: this.parts.length,
      totalTokens,
      averageTokensPerPart: Math.floor(totalTokens / this.parts.length)
    };
  }
}

// Cost calculator for different strategies
export function calculateResponseCost(
  tokens: number, 
  modelConfig: ModelConfig, 
  strategy: 'single' | 'chunked' | 'streaming'
): {
  estimatedCost: number;
  costPerToken: number;
  strategy: string;
  recommendation: string;
} {
  const baseCost = (tokens / 1000000) * modelConfig.costPerToken;
  
  // Add overhead costs for different strategies
  const overheadMultiplier = {
    'single': 1.0,
    'chunked': 1.1, // 10% overhead for chunking
    'streaming': 1.2 // 20% overhead for streaming
  };
  
  const estimatedCost = baseCost * overheadMultiplier[strategy];
  
  let recommendation = "";
  if (tokens < 2000) {
    recommendation = "Use single response for optimal cost";
  } else if (tokens < 6000) {
    recommendation = "Consider chunking for better user experience";
  } else {
    recommendation = "Use streaming for large responses";
  }
  
  return {
    estimatedCost,
    costPerToken: modelConfig.costPerToken,
    strategy,
    recommendation
  };
}
