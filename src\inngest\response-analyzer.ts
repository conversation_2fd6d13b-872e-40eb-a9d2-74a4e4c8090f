import { estimateTokenCount } from "./utils";
import { ModelConfig } from "./model-config";

// Response size prediction based on prompt analysis
export interface ResponsePrediction {
  estimatedTokens: number;
  confidence: number; // 0-1 scale
  category: 'small' | 'medium' | 'large' | 'very_large';
  recommendedStrategy: 'single' | 'chunked' | 'streaming' | 'continuation';
  reasoning: string[];
}

export class ResponseSizeAnalyzer {
  private modelConfig: ModelConfig;

  constructor(modelConfig: ModelConfig) {
    this.modelConfig = modelConfig;
  }

  // Analyze prompt to predict response size
  analyzePrompt(prompt: string): ResponsePrediction {
    const promptTokens = estimateTokenCount(prompt);
    const reasoning: string[] = [];
    let estimatedTokens = 0;
    let confidence = 0.7; // Base confidence

    // Base estimation: response is typically 1-3x prompt length
    let multiplier = 1.5;

    // Analyze prompt characteristics
    const promptLower = prompt.toLowerCase();
    
    // Code generation requests tend to produce longer responses
    if (this.containsCodeKeywords(promptLower)) {
      multiplier *= 2.5;
      reasoning.push("Code generation detected - responses typically longer");
      confidence += 0.1;
    }

    // Explanation requests
    if (this.containsExplanationKeywords(promptLower)) {
      multiplier *= 1.8;
      reasoning.push("Explanation request - detailed response expected");
      confidence += 0.05;
    }

    // List/enumeration requests
    if (this.containsListKeywords(promptLower)) {
      multiplier *= 2.0;
      reasoning.push("List/enumeration request - structured response expected");
      confidence += 0.1;
    }

    // Tutorial/step-by-step requests
    if (this.containsTutorialKeywords(promptLower)) {
      multiplier *= 3.0;
      reasoning.push("Tutorial request - comprehensive response expected");
      confidence += 0.15;
    }

    // Complex technical requests
    if (this.containsComplexKeywords(promptLower)) {
      multiplier *= 2.2;
      reasoning.push("Complex technical request - detailed response needed");
      confidence += 0.1;
    }

    // Short answer indicators
    if (this.containsShortAnswerKeywords(promptLower)) {
      multiplier *= 0.6;
      reasoning.push("Short answer request - concise response expected");
      confidence += 0.1;
    }

    // Adjust for prompt length
    if (promptTokens > 1000) {
      multiplier *= 1.3;
      reasoning.push("Long prompt - proportionally longer response expected");
    } else if (promptTokens < 100) {
      multiplier *= 0.8;
      reasoning.push("Short prompt - shorter response likely");
    }

    estimatedTokens = Math.floor(promptTokens * multiplier);
    confidence = Math.min(0.95, confidence); // Cap confidence at 95%

    // Categorize response size
    let category: ResponsePrediction['category'];
    let recommendedStrategy: ResponsePrediction['recommendedStrategy'];

    if (estimatedTokens < this.modelConfig.maxTokens * 0.5) {
      category = 'small';
      recommendedStrategy = 'single';
    } else if (estimatedTokens < this.modelConfig.maxTokens * 0.8) {
      category = 'medium';
      recommendedStrategy = 'single';
    } else if (estimatedTokens < this.modelConfig.maxTokens * 1.5) {
      category = 'large';
      recommendedStrategy = 'chunked';
    } else {
      category = 'very_large';
      recommendedStrategy = 'streaming';
    }

    return {
      estimatedTokens,
      confidence,
      category,
      recommendedStrategy,
      reasoning
    };
  }

  private containsCodeKeywords(prompt: string): boolean {
    const codeKeywords = [
      'code', 'function', 'class', 'method', 'algorithm', 'implement',
      'programming', 'script', 'api', 'database', 'sql', 'javascript',
      'python', 'react', 'component', 'html', 'css', 'typescript',
      'build', 'create app', 'develop', 'framework'
    ];
    return codeKeywords.some(keyword => prompt.includes(keyword));
  }

  private containsExplanationKeywords(prompt: string): boolean {
    const explanationKeywords = [
      'explain', 'how does', 'why', 'what is', 'describe', 'detail',
      'elaborate', 'clarify', 'breakdown', 'analyze', 'discuss'
    ];
    return explanationKeywords.some(keyword => prompt.includes(keyword));
  }

  private containsListKeywords(prompt: string): boolean {
    const listKeywords = [
      'list', 'enumerate', 'steps', 'process', 'workflow', 'checklist',
      'items', 'points', 'features', 'options', 'alternatives'
    ];
    return listKeywords.some(keyword => prompt.includes(keyword));
  }

  private containsTutorialKeywords(prompt: string): boolean {
    const tutorialKeywords = [
      'tutorial', 'guide', 'walkthrough', 'step by step', 'how to',
      'complete guide', 'comprehensive', 'from scratch', 'beginner',
      'learn', 'teach', 'course'
    ];
    return tutorialKeywords.some(keyword => prompt.includes(keyword));
  }

  private containsComplexKeywords(prompt: string): boolean {
    const complexKeywords = [
      'architecture', 'design pattern', 'optimization', 'performance',
      'scalability', 'security', 'advanced', 'enterprise', 'production',
      'best practices', 'comprehensive solution'
    ];
    return complexKeywords.some(keyword => prompt.includes(keyword));
  }

  private containsShortAnswerKeywords(prompt: string): boolean {
    const shortKeywords = [
      'yes or no', 'true or false', 'quick', 'brief', 'short',
      'simple', 'just', 'only', 'summary', 'tldr'
    ];
    return shortKeywords.some(keyword => prompt.includes(keyword));
  }

  // Get optimal strategy based on prediction
  getOptimalStrategy(prediction: ResponsePrediction): {
    strategy: string;
    modelAdjustments: Partial<ModelConfig>;
    processingOptions: {
      useStreaming: boolean;
      enableContinuation: boolean;
      chunkSize?: number;
      maxContinuations?: number;
    };
  } {
    const baseOptions = {
      useStreaming: false,
      enableContinuation: false
    };

    switch (prediction.recommendedStrategy) {
      case 'single':
        return {
          strategy: 'single_response',
          modelAdjustments: {},
          processingOptions: baseOptions
        };

      case 'chunked':
        return {
          strategy: 'chunked_response',
          modelAdjustments: {
            maxTokens: Math.floor(this.modelConfig.maxTokens * 0.8)
          },
          processingOptions: {
            ...baseOptions,
            chunkSize: Math.floor(this.modelConfig.maxTokens * 0.7)
          }
        };

      case 'streaming':
        return {
          strategy: 'streaming_response',
          modelAdjustments: {
            maxTokens: Math.floor(this.modelConfig.maxTokens * 0.9)
          },
          processingOptions: {
            useStreaming: true,
            enableContinuation: false,
            chunkSize: Math.floor(this.modelConfig.maxTokens * 0.8)
          }
        };

      case 'continuation':
        return {
          strategy: 'continuation_response',
          modelAdjustments: {
            maxTokens: this.modelConfig.maxTokens
          },
          processingOptions: {
            useStreaming: true,
            enableContinuation: true,
            maxContinuations: 5,
            chunkSize: Math.floor(this.modelConfig.maxTokens * 0.9)
          }
        };

      default:
        return {
          strategy: 'single_response',
          modelAdjustments: {},
          processingOptions: baseOptions
        };
    }
  }
}
