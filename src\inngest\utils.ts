import {Sandbox} from "@e2b/code-interpreter";
import { AgentResult, Message, TextMessage } from "@inngest/agent-kit";
import { SANDBOX_TIMEOUT } from "./type";

export async function getSandbox(sandboxId: string){
    const sandbox = await Sandbox.connect(sandboxId)
    await sandbox.setTimeout(SANDBOX_TIMEOUT);
    return sandbox;
};

export function lastAssistantTextMessageContent(result: AgentResult) {
    const lastAssistantTextMessageIndex = result.output.findLastIndex(
        (message) => message.role == "assistant",
    );
    const message = result.output[lastAssistantTextMessageIndex] as 
    | TextMessage
    | undefined;
  return message?.content
    ? typeof message.content === "string"
        ? message.content
        : message?.content.map((c) => c.text).join("")
    : undefined;

};

export const parseAgentOutput = (value: Message[]) => {
        const output = value[0];
        if (output.type !== "text"){
          return "Fragment";
        }

        if (Array.isArray(output.content)){
          return output.content.map((txt)=>txt).join("");
        } else{
          return output.content
        }
      };

// Estimate token count for text (rough approximation: 1 token ≈ 4 characters)
export function estimateTokenCount(text: string): number {
  return Math.ceil(text.length / 4);
}

// Check if response is likely to exceed token limits
export function isResponseTooLarge(text: string, maxTokens: number = 4000): boolean {
  return estimateTokenCount(text) > maxTokens;
}

// Create chunked response system for large outputs
export interface ChunkedResponse {
  chunks: string[];
  totalChunks: number;
  estimatedTokens: number;
}

export function createChunkedResponse(text: string, maxTokensPerChunk: number = 3500): ChunkedResponse {
  const maxCharsPerChunk = maxTokensPerChunk * 4; // Rough conversion
  const chunks: string[] = [];

  // Split by sentences first to maintain coherence
  const sentences = text.split(/(?<=[.!?])\s+/);
  let currentChunk = "";

  for (const sentence of sentences) {
    const potentialChunk = currentChunk + (currentChunk ? " " : "") + sentence;

    if (potentialChunk.length > maxCharsPerChunk && currentChunk) {
      chunks.push(currentChunk.trim());
      currentChunk = sentence;
    } else {
      currentChunk = potentialChunk;
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  return {
    chunks,
    totalChunks: chunks.length,
    estimatedTokens: estimateTokenCount(text)
  };
}