import { anthropic, openai } from "@inngest/agent-kit";

// Model configuration with token limits and cost considerations
export interface ModelConfig {
  name: string;
  maxTokens: number;
  costPerToken: number; // Cost per 1K tokens (output)
  provider: 'anthropic' | 'openai';
  model: string;
  supportsStreaming: boolean;
}

export const MODEL_CONFIGS: Record<string, ModelConfig> = {
  // ===== STATE-OF-THE-ART MODELS FOR NEXT.JS PROJECT GENERATION =====

  'claude-opus': {
    name: 'Claude 3 Opus (Premium)',
    maxTokens: 8192,
    costPerToken: 75, // $75 per 1M output tokens - Premium quality
    provider: 'anthropic',
    model: 'claude-3-opus-20240229',
    supportsStreaming: true
  },
  'claude-sonnet-latest': {
    name: 'Claude 3.5 Sonnet (Latest)',
    maxTokens: 8192, // With beta header for maximum output
    costPerToken: 15, // $15 per 1M output tokens - Best balance
    provider: 'anthropic',
    model: 'claude-3-5-sonnet-20241022',
    supportsStreaming: true
  },
  'gpt-4o': {
    name: 'GPT-4o (Flagship)',
    maxTokens: 16384, // Large context window
    costPerToken: 30, // $30 per 1M output tokens - High quality
    provider: 'openai',
    model: 'gpt-4o',
    supportsStreaming: true
  },
  'gpt-4-turbo': {
    name: 'GPT-4 Turbo',
    maxTokens: 16384,
    costPerToken: 30, // $30 per 1M output tokens - Fast & capable
    provider: 'openai',
    model: 'gpt-4-turbo',
    supportsStreaming: true
  },
  'o1-preview': {
    name: 'OpenAI o1-preview (Reasoning)',
    maxTokens: 32768, // Massive context for complex projects
    costPerToken: 60, // $60 per 1M output tokens - Advanced reasoning
    provider: 'openai',
    model: 'o1-preview',
    supportsStreaming: false // o1 models don't support streaming
  },
  'o1-mini': {
    name: 'OpenAI o1-mini (Reasoning)',
    maxTokens: 16384,
    costPerToken: 12, // $12 per 1M output tokens - Efficient reasoning
    provider: 'openai',
    model: 'o1-mini',
    supportsStreaming: false // o1 models don't support streaming
  },

  // ===== COST-EFFECTIVE MODELS (COMMENTED FOR REFERENCE) =====
  /*
  'claude-haiku': {
    name: 'Claude 3 Haiku (Cost-Effective)',
    maxTokens: 4096,
    costPerToken: 1.25, // $1.25 per 1M output tokens - Most affordable
    provider: 'anthropic',
    model: 'claude-3-haiku-20240307',
    supportsStreaming: true
  },
  'gpt-4o-mini': {
    name: 'GPT-4o Mini (Cost-Effective)',
    maxTokens: 16384,
    costPerToken: 0.6, // $0.60 per 1M output tokens - Ultra affordable
    provider: 'openai',
    model: 'gpt-4o-mini',
    supportsStreaming: true
  },
  'claude-sonnet-legacy': {
    name: 'Claude 3.5 Sonnet (Legacy)',
    maxTokens: 4096, // Without beta header
    costPerToken: 15, // $15 per 1M output tokens
    provider: 'anthropic',
    model: 'claude-3-5-sonnet-20241022',
    supportsStreaming: true
  }
  */
};

// Environment-based model preferences for state-of-the-art Next.js generation
export function getEnvironmentPreferences(): {
  preferredModel: string;
  fallbackModel: string;
  maxCostPerRequest: number;
} {
  // Default to premium models for best Next.js project generation quality
  const preferredModel = process.env.PREFERRED_MODEL || 'claude-sonnet-latest'; // Best balance of quality & speed
  const fallbackModel = process.env.FALLBACK_MODEL || 'gpt-4o'; // High-quality fallback
  const maxCostPerRequest = parseFloat(process.env.MAX_COST_PER_REQUEST || '0.50'); // $0.50 for premium quality

  return {
    preferredModel,
    fallbackModel,
    maxCostPerRequest
  };
}

// Strategy for choosing the best model based on estimated response size and complexity
export function selectOptimalModel(
  estimatedTokens: number,
  complexity: 'simple' | 'moderate' | 'complex' = 'moderate',
  prioritizeQuality: boolean = true
): ModelConfig {
  const configs = Object.values(MODEL_CONFIGS);
  const envPrefs = getEnvironmentPreferences();

  // For complex Next.js projects, prefer reasoning models
  if (complexity === 'complex' && estimatedTokens <= 16384) {
    const o1Model = MODEL_CONFIGS['o1-mini'];
    if (o1Model) {
      const estimatedCost = (estimatedTokens / 1000) * o1Model.costPerToken;
      if (estimatedCost <= envPrefs.maxCostPerRequest) {
        return o1Model;
      }
    }
  }

  // For very large projects, use o1-preview if within budget
  if (estimatedTokens > 16384 && estimatedTokens <= 32768) {
    const o1Preview = MODEL_CONFIGS['o1-preview'];
    if (o1Preview) {
      const estimatedCost = (estimatedTokens / 1000) * o1Preview.costPerToken;
      if (estimatedCost <= envPrefs.maxCostPerRequest) {
        return o1Preview;
      }
    }
  }

  // Try preferred model first if it can handle the tokens
  const preferredConfig = MODEL_CONFIGS[envPrefs.preferredModel];
  if (preferredConfig && preferredConfig.maxTokens >= estimatedTokens) {
    const estimatedCost = (estimatedTokens / 1000) * preferredConfig.costPerToken;
    if (estimatedCost <= envPrefs.maxCostPerRequest) {
      return preferredConfig;
    }
  }

  // Filter models that can handle the estimated tokens and are within budget
  const suitableModels = configs.filter(config => {
    const estimatedCost = (estimatedTokens / 1000) * config.costPerToken;
    return config.maxTokens >= estimatedTokens && estimatedCost <= envPrefs.maxCostPerRequest;
  });

  if (suitableModels.length === 0) {
    // If no model fits budget, use the highest quality that can handle tokens
    const capableModels = configs.filter(config => config.maxTokens >= estimatedTokens);
    if (capableModels.length > 0) {
      // Sort by quality (higher cost usually means better quality)
      return prioritizeQuality
        ? capableModels.sort((a, b) => b.costPerToken - a.costPerToken)[0]
        : capableModels.sort((a, b) => a.costPerToken - b.costPerToken)[0];
    }

    // Last resort: use fallback model with chunking
    return MODEL_CONFIGS[envPrefs.fallbackModel] || MODEL_CONFIGS['claude-sonnet-latest'];
  }

  // Sort by quality if prioritizing quality, otherwise by cost
  return prioritizeQuality
    ? suitableModels.sort((a, b) => b.maxTokens - a.maxTokens || b.costPerToken - a.costPerToken)[0]
    : suitableModels.sort((a, b) => a.costPerToken - b.costPerToken)[0];
}

// Create model instance with optimal configuration for Next.js generation
export function createOptimalModel(config: ModelConfig, temperature: number = 0.3) {
  // Lower temperature for more consistent code generation
  const isO1Model = config.model.startsWith('o1-');

  // o1 models don't support temperature parameter
  const baseParams = isO1Model
    ? { max_tokens: config.maxTokens }
    : { temperature, max_tokens: config.maxTokens };

  if (config.provider === 'anthropic') {
    const params = config.model.includes('sonnet') || config.model.includes('opus')
      ? {
          ...baseParams,
          // Add beta header for 8K tokens on Sonnet/Opus
          extra_headers: { 'anthropic-beta': 'max-tokens-3-5-sonnet-2024-07-15' }
        }
      : baseParams;

    return anthropic({
      model: config.model,
      defaultParameters: params
    });
  } else {
    // OpenAI models (including o1 series)
    return openai({
      model: config.model,
      defaultParameters: baseParams
    });
  }
}

// Response continuation system for handling large outputs
export interface ContinuationContext {
  previousContent: string;
  remainingContent: string;
  chunkIndex: number;
  totalChunks: number;
  modelConfig: ModelConfig;
}

export function createContinuationPrompt(context: ContinuationContext): string {
  return `Continue the previous response. This is chunk ${context.chunkIndex + 1} of ${context.totalChunks}.

Previous content summary: ${context.previousContent.slice(-500)}...

Continue from where you left off, maintaining the same tone and context. Do not repeat the previous content.`;
}

// Adaptive token allocation for Next.js project generation
export function calculateOptimalTokens(
  promptLength: number,
  responseType: 'code' | 'text' | 'mixed',
  projectComplexity: 'simple' | 'moderate' | 'complex' = 'moderate'
): number {
  const baseTokens = {
    'code': {
      'simple': 8000,    // Simple components, basic functionality
      'moderate': 12000, // Full-featured components with state management
      'complex': 20000   // Complex applications with multiple files
    },
    'text': {
      'simple': 2000,    // Brief explanations
      'moderate': 4000,  // Detailed documentation
      'complex': 6000    // Comprehensive guides
    },
    'mixed': {
      'simple': 6000,    // Code + basic explanations
      'moderate': 10000, // Code + detailed comments + docs
      'complex': 16000   // Full project with documentation
    }
  };

  // Adjust based on prompt length (longer prompts often need longer responses)
  const promptFactor = Math.min(2.5, 1 + (promptLength / 8000));

  // Get base tokens for the complexity level
  const baseTokenCount = baseTokens[responseType][projectComplexity];

  return Math.floor(baseTokenCount * promptFactor);
}

// Enhanced model selection for specific Next.js use cases
export function selectModelForNextJSTask(taskType: 'component' | 'page' | 'api' | 'fullstack' | 'architecture'): ModelConfig {
  const envPrefs = getEnvironmentPreferences();

  switch (taskType) {
    case 'component':
      // For individual components, use fast and efficient models
      return MODEL_CONFIGS['claude-sonnet-latest'] || MODEL_CONFIGS['gpt-4o'];

    case 'page':
      // For full pages with multiple components
      return MODEL_CONFIGS['gpt-4o'] || MODEL_CONFIGS['claude-sonnet-latest'];

    case 'api':
      // For API routes and backend logic
      return MODEL_CONFIGS['claude-sonnet-latest'] || MODEL_CONFIGS['gpt-4-turbo'];

    case 'fullstack':
      // For complete applications with frontend + backend
      return MODEL_CONFIGS['o1-mini'] || MODEL_CONFIGS['claude-opus'];

    case 'architecture':
      // For complex architectural decisions and planning
      return MODEL_CONFIGS['o1-preview'] || MODEL_CONFIGS['claude-opus'];

    default:
      return MODEL_CONFIGS[envPrefs.preferredModel] || MODEL_CONFIGS['claude-sonnet-latest'];
  }
}
