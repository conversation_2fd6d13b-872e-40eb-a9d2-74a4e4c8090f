import { anthropic, openai } from "@inngest/agent-kit";

// Model configuration with token limits and cost considerations
export interface ModelConfig {
  name: string;
  maxTokens: number;
  costPerToken: number; // Cost per 1K tokens (output)
  provider: 'anthropic' | 'openai';
  model: string;
  supportsStreaming: boolean;
}

export const MODEL_CONFIGS: Record<string, ModelConfig> = {
  'claude-haiku': {
    name: 'Claude 3 Haiku',
    maxTokens: 4096,
    costPerToken: 1.25, // $1.25 per 1M output tokens
    provider: 'anthropic',
    model: 'claude-3-haiku-20240307',
    supportsStreaming: true
  },
  'claude-sonnet': {
    name: 'Claude 3.5 Sonnet',
    maxTokens: 8192, // With beta header
    costPerToken: 15, // $15 per 1M output tokens
    provider: 'anthropic',
    model: 'claude-3-5-sonnet-20241022',
    supportsStreaming: true
  },
  'claude-sonnet-legacy': {
    name: 'Claude 3.5 Sonnet (Legacy)',
    maxTokens: 4096, // Without beta header
    costPerToken: 15, // $15 per 1M output tokens
    provider: 'anthropic',
    model: 'claude-3-5-sonnet-20241022',
    supportsStreaming: true
  },
  'gpt-4o-mini': {
    name: 'GPT-4o Mini',
    maxTokens: 16384,
    costPerToken: 0.6, // $0.60 per 1M output tokens
    provider: 'openai',
    model: 'gpt-4o-mini',
    supportsStreaming: true
  }
};

// Environment-based model preferences
export function getEnvironmentPreferences(): {
  preferredModel: string;
  fallbackModel: string;
  maxCostPerRequest: number;
} {
  // Check for environment variables or default to cost-effective options
  const preferredModel = process.env.PREFERRED_MODEL || 'gpt-4o-mini'; // Most cost-effective
  const fallbackModel = process.env.FALLBACK_MODEL || 'claude-haiku';
  const maxCostPerRequest = parseFloat(process.env.MAX_COST_PER_REQUEST || '0.01'); // $0.01 default

  return {
    preferredModel,
    fallbackModel,
    maxCostPerRequest
  };
}

// Strategy for choosing the best model based on estimated response size
export function selectOptimalModel(estimatedTokens: number, prioritizeCost: boolean = true): ModelConfig {
  const configs = Object.values(MODEL_CONFIGS);
  const envPrefs = getEnvironmentPreferences();

  // Try preferred model first if it can handle the tokens
  const preferredConfig = MODEL_CONFIGS[envPrefs.preferredModel];
  if (preferredConfig && preferredConfig.maxTokens >= estimatedTokens) {
    const estimatedCost = (estimatedTokens / 1000) * preferredConfig.costPerToken;
    if (estimatedCost <= envPrefs.maxCostPerRequest) {
      return preferredConfig;
    }
  }

  // Filter models that can handle the estimated tokens and are within budget
  const suitableModels = configs.filter(config => {
    const estimatedCost = (estimatedTokens / 1000) * config.costPerToken;
    return config.maxTokens >= estimatedTokens && estimatedCost <= envPrefs.maxCostPerRequest;
  });

  if (suitableModels.length === 0) {
    // If no model fits budget, use the most cost-effective that can handle tokens
    const affordableModels = configs.filter(config => config.maxTokens >= estimatedTokens);
    if (affordableModels.length > 0) {
      return affordableModels.sort((a, b) => a.costPerToken - b.costPerToken)[0];
    }

    // Last resort: use fallback model with chunking
    return MODEL_CONFIGS[envPrefs.fallbackModel] || MODEL_CONFIGS['claude-haiku'];
  }

  // Sort by cost if prioritizing cost, otherwise by capability
  return prioritizeCost
    ? suitableModels.sort((a, b) => a.costPerToken - b.costPerToken)[0]
    : suitableModels.sort((a, b) => b.maxTokens - a.maxTokens)[0];
}

// Create model instance with optimal configuration
export function createOptimalModel(config: ModelConfig, temperature: number = 0.5) {
  const baseParams = { 
    temperature, 
    max_tokens: config.maxTokens 
  };
  
  if (config.provider === 'anthropic') {
    const params = config.model.includes('sonnet') 
      ? { 
          ...baseParams, 
          // Add beta header for 8K tokens on Sonnet
          extra_headers: { 'anthropic-beta': 'max-tokens-3-5-sonnet-2024-07-15' }
        }
      : baseParams;
      
    return anthropic({ 
      model: config.model,
      defaultParameters: params
    });
  } else {
    return openai({ 
      model: config.model,
      defaultParameters: baseParams
    });
  }
}

// Response continuation system for handling large outputs
export interface ContinuationContext {
  previousContent: string;
  remainingContent: string;
  chunkIndex: number;
  totalChunks: number;
  modelConfig: ModelConfig;
}

export function createContinuationPrompt(context: ContinuationContext): string {
  return `Continue the previous response. This is chunk ${context.chunkIndex + 1} of ${context.totalChunks}.

Previous content summary: ${context.previousContent.slice(-500)}...

Continue from where you left off, maintaining the same tone and context. Do not repeat the previous content.`;
}

// Adaptive token allocation based on prompt complexity
export function calculateOptimalTokens(promptLength: number, responseType: 'code' | 'text' | 'mixed'): number {
  const baseTokens = {
    'code': 6000,     // Code responses tend to be longer
    'text': 3000,     // Text responses are usually shorter
    'mixed': 4500     // Mixed content needs balance
  };
  
  // Adjust based on prompt length (longer prompts often need longer responses)
  const promptFactor = Math.min(2, 1 + (promptLength / 10000));
  
  return Math.floor(baseTokens[responseType] * promptFactor);
}
