import { anthropic, openai } from "@inngest/agent-kit";

// Model configuration with token limits and cost considerations
export interface ModelConfig {
  name: string;
  maxTokens: number;
  costPerToken: number; // Cost per 1M tokens (output)
  provider: 'anthropic' | 'openai';
  model: string;
  supportsStreaming: boolean;
}

export const MODEL_CONFIGS: Record<string, ModelConfig> = {
  // ===== STATE-OF-THE-ART MODELS FOR NEXT.JS PROJECT GENERATION =====

  'claude-opus': {
    name: 'Claude 3 Opus (Premium)',
    maxTokens: 8192,
    costPerToken: 75, // $75 per 1M output tokens - Premium quality
    provider: 'anthropic',
    model: 'claude-3-opus-20240229',
    supportsStreaming: true
  },
  'claude-sonnet-latest': {
    name: 'Claude 3.5 Sonnet (Latest)',
    maxTokens: 8192, // With beta header for maximum output
    costPerToken: 15, // $15 per 1M output tokens - Best balance
    provider: 'anthropic',
    model: 'claude-3-5-sonnet-20241022',
    supportsStreaming: true
  },
  'gpt-4o': {
    name: 'GPT-4o (Flagship)',
    maxTokens: 16384, // Large context window
    costPerToken: 30, // $30 per 1M output tokens - High quality
    provider: 'openai',
    model: 'gpt-4o',
    supportsStreaming: true
  },
  'gpt-4-turbo': {
    name: 'GPT-4 Turbo',
    maxTokens: 16384,
    costPerToken: 30, // $30 per 1M output tokens - Fast & capable
    provider: 'openai',
    model: 'gpt-4-turbo',
    supportsStreaming: true
  },
  'o1-preview': {
    name: 'OpenAI o1-preview (Reasoning)',
    maxTokens: 32768, // Massive context for complex projects
    costPerToken: 60, // $60 per 1M output tokens - Advanced reasoning
    provider: 'openai',
    model: 'o1-preview',
    supportsStreaming: false // o1 models don't support streaming
  },
  'o1-mini': {
    name: 'OpenAI o1-mini (Reasoning)',
    maxTokens: 16384,
    costPerToken: 12, // $12 per 1M output tokens - Efficient reasoning
    provider: 'openai',
    model: 'o1-mini',
    supportsStreaming: false // o1 models don't support streaming
  },

  // ===== COST-EFFECTIVE MODELS (COMMENTED FOR REFERENCE) =====
  /*
  'claude-haiku': {
    name: 'Claude 3 Haiku (Cost-Effective)',
    maxTokens: 4096,
    costPerToken: 1.25, // $1.25 per 1M output tokens - Most affordable
    provider: 'anthropic',
    model: 'claude-3-haiku-20240307',
    supportsStreaming: true
  },
  'gpt-4o-mini': {
    name: 'GPT-4o Mini (Cost-Effective)',
    maxTokens: 16384,
    costPerToken: 0.6, // $0.60 per 1M output tokens - Ultra affordable
    provider: 'openai',
    model: 'gpt-4o-mini',
    supportsStreaming: true
  },
  'claude-sonnet-legacy': {
    name: 'Claude 3.5 Sonnet (Legacy)',
    maxTokens: 4096, // Without beta header
    costPerToken: 15, // $15 per 1M output tokens
    provider: 'anthropic',
    model: 'claude-3-5-sonnet-20241022',
    supportsStreaming: true
  }
  */
};

// Environment-based model preferences for state-of-the-art Next.js generation
export function getEnvironmentPreferences(): {
  preferredModel: string;
  fallbackModel: string;
  maxCostPerRequest: number;
} {
  // Default to premium models for best Next.js project generation quality
  const preferredModel = process.env.PREFERRED_MODEL || 'claude-opus'; // Most advanced model for complex web apps
  const fallbackModel = process.env.FALLBACK_MODEL || 'gpt-4o'; // High-quality fallback
  const maxCostPerRequest = parseFloat(process.env.MAX_COST_PER_REQUEST || '2.00'); // $2.00 for premium quality

  console.log('Environment preferences:', { preferredModel, fallbackModel, maxCostPerRequest });

  return {
    preferredModel,
    fallbackModel,
    maxCostPerRequest
  };
}

// Strategy for choosing the best model based on estimated response size and complexity
export function selectOptimalModel(
  estimatedTokens: number,
  complexity: 'simple' | 'moderate' | 'complex' = 'moderate',
  prioritizeQuality: boolean = true
): ModelConfig {
  // Filter out o1 models due to system message limitations
  const configs = Object.values(MODEL_CONFIGS).filter(config =>
    !config.name.includes('o1-preview') && !config.name.includes('o1-mini')
  );
  const envPrefs = getEnvironmentPreferences();

  console.log(`Model selection for ${estimatedTokens} tokens, complexity: ${complexity}`);
  console.log(`Available models after filtering:`, configs.map(c => c.name));

  // For advanced web applications, prioritize the most capable models
  // Claude Opus: Best for complex architecture and sophisticated web apps
  // GPT-4o: Excellent for large context and comprehensive applications

  if (complexity === 'complex' || estimatedTokens > 8000) {
    // First try Claude Opus - best for complex web development
    const claudeOpus = MODEL_CONFIGS['claude-opus'];
    console.log(`Claude Opus check: ${estimatedTokens} tokens <= ${claudeOpus?.maxTokens} max tokens`);
    if (claudeOpus && claudeOpus.maxTokens >= estimatedTokens) {
      const estimatedCost = (estimatedTokens / 1000000) * claudeOpus.costPerToken;
      console.log(`Claude Opus cost check: $${estimatedCost.toFixed(4)} <= $${envPrefs.maxCostPerRequest}`);
      if (estimatedCost <= envPrefs.maxCostPerRequest) {
        console.log('✅ Selected Claude Opus for complex task');
        return claudeOpus;
      } else {
        console.log('❌ Claude Opus rejected: cost too high');
      }
    } else {
      console.log('❌ Claude Opus rejected: token limit exceeded');
    }

    // Fallback to GPT-4o for large context requirements
    const gpt4o = MODEL_CONFIGS['gpt-4o'];
    if (gpt4o && gpt4o.maxTokens >= estimatedTokens) {
      const estimatedCost = (estimatedTokens / 1000000) * gpt4o.costPerToken;
      if (estimatedCost <= envPrefs.maxCostPerRequest) {
        return gpt4o;
      }
    }
  }

  // For moderate complexity, prefer Claude Sonnet Latest (best balance)
  if (complexity === 'moderate') {
    const claudeSonnet = MODEL_CONFIGS['claude-sonnet-latest'];
    if (claudeSonnet && claudeSonnet.maxTokens >= estimatedTokens) {
      const estimatedCost = (estimatedTokens / 1000000) * claudeSonnet.costPerToken;
      if (estimatedCost <= envPrefs.maxCostPerRequest) {
        return claudeSonnet;
      }
    }
  }

  // Try preferred model first if it can handle the tokens
  const preferredConfig = MODEL_CONFIGS[envPrefs.preferredModel];
  if (preferredConfig && preferredConfig.maxTokens >= estimatedTokens) {
    const estimatedCost = (estimatedTokens / 1000000) * preferredConfig.costPerToken;
    if (estimatedCost <= envPrefs.maxCostPerRequest) {
      return preferredConfig;
    }
  }

  // Filter models that can handle the estimated tokens and are within budget
  const suitableModels = configs.filter(config => {
    const estimatedCost = (estimatedTokens / 1000000) * config.costPerToken;
    return config.maxTokens >= estimatedTokens && estimatedCost <= envPrefs.maxCostPerRequest;
  });

  if (suitableModels.length === 0) {
    // If no model fits budget, use the highest quality that can handle tokens
    const capableModels = configs.filter(config => config.maxTokens >= estimatedTokens);
    if (capableModels.length > 0) {
      // Sort by quality (higher cost usually means better quality)
      return prioritizeQuality
        ? capableModels.sort((a, b) => b.costPerToken - a.costPerToken)[0]
        : capableModels.sort((a, b) => a.costPerToken - b.costPerToken)[0];
    }

    // Last resort: use fallback model with chunking
    return MODEL_CONFIGS[envPrefs.fallbackModel] || MODEL_CONFIGS['claude-sonnet-latest'];
  }

  // Sort by quality if prioritizing quality, otherwise by cost
  return prioritizeQuality
    ? suitableModels.sort((a, b) => b.maxTokens - a.maxTokens || b.costPerToken - a.costPerToken)[0]
    : suitableModels.sort((a, b) => a.costPerToken - b.costPerToken)[0];
}

// Create model instance with optimal configuration for Next.js generation
export function createOptimalModel(config: ModelConfig, temperature: number = 0.3) {
  // Lower temperature for more consistent code generation
  const isO1Model = config.model.startsWith('o1-');

  // o1 models don't support temperature parameter
  const baseParams = isO1Model
    ? { max_tokens: config.maxTokens }
    : { temperature, max_tokens: config.maxTokens };

  if (config.provider === 'anthropic') {
    const params = config.model.includes('sonnet') || config.model.includes('opus')
      ? {
          ...baseParams,
          // Add beta header for 8K tokens on Sonnet/Opus
          extra_headers: { 'anthropic-beta': 'max-tokens-3-5-sonnet-2024-07-15' }
        }
      : baseParams;

    return anthropic({
      model: config.model,
      defaultParameters: params
    });
  } else {
    // OpenAI models (including o1 series)
    const modelInstance = openai({
      model: config.model,
      defaultParameters: baseParams
    });

    // Mark o1 models as not supporting system messages
    if (isO1Model) {
      (modelInstance as any).supportsSystemMessages = false;
    }

    return modelInstance;
  }
}

// Check if a model supports system messages
export function supportsSystemMessages(config: ModelConfig): boolean {
  // o1 models don't support system messages
  if (config.model.startsWith('o1-')) {
    return false;
  }

  // All other models support system messages
  return true;
}

// Response continuation system for handling large outputs
export interface ContinuationContext {
  previousContent: string;
  remainingContent: string;
  chunkIndex: number;
  totalChunks: number;
  modelConfig: ModelConfig;
}

export function createContinuationPrompt(context: ContinuationContext): string {
  return `Continue the previous response. This is chunk ${context.chunkIndex + 1} of ${context.totalChunks}.

Previous content summary: ${context.previousContent.slice(-500)}...

Continue from where you left off, maintaining the same tone and context. Do not repeat the previous content.`;
}

// Adaptive token allocation for Next.js project generation
export function calculateOptimalTokens(
  promptLength: number,
  responseType: 'code' | 'text' | 'mixed',
  projectComplexity: 'simple' | 'moderate' | 'complex' = 'moderate'
): number {
  const baseTokens = {
    'code': {
      'simple': 10000,   // Simple components with modern patterns
      'moderate': 16000, // Full-featured components with advanced state management
      'complex': 24000   // Complex applications with multiple files and advanced features
    },
    'text': {
      'simple': 3000,    // Detailed explanations with examples
      'moderate': 6000,  // Comprehensive documentation
      'complex': 8000    // In-depth guides with best practices
    },
    'mixed': {
      'simple': 8000,    // Code + detailed explanations
      'moderate': 14000, // Code + comprehensive comments + docs
      'complex': 20000   // Full project with extensive documentation
    }
  };

  // Adjust based on prompt length (longer prompts often need longer responses)
  const promptFactor = Math.min(2.5, 1 + (promptLength / 8000));

  // Get base tokens for the complexity level
  const baseTokenCount = baseTokens[responseType][projectComplexity];

  return Math.floor(baseTokenCount * promptFactor);
}

// Enhanced model selection for advanced web application development
export function selectModelForAdvancedWebApp(taskType: 'component' | 'page' | 'api' | 'fullstack' | 'architecture' | 'dashboard' | 'ecommerce' | 'saas'): ModelConfig {
  const envPrefs = getEnvironmentPreferences();

  switch (taskType) {
    case 'component':
      // For sophisticated UI components with advanced interactions
      return MODEL_CONFIGS['claude-sonnet-latest'] || MODEL_CONFIGS['gpt-4o'];

    case 'page':
      // For complex pages with multiple interactive components
      return MODEL_CONFIGS['claude-opus'] || MODEL_CONFIGS['gpt-4o'];

    case 'api':
      // For robust API routes with advanced business logic
      return MODEL_CONFIGS['claude-opus'] || MODEL_CONFIGS['gpt-4o'];

    case 'fullstack':
      // For complete applications with advanced features
      return MODEL_CONFIGS['claude-opus'] || MODEL_CONFIGS['gpt-4o'];

    case 'architecture':
      // For complex architectural decisions and system design
      return MODEL_CONFIGS['claude-opus'] || MODEL_CONFIGS['gpt-4o'];

    case 'dashboard':
      // For advanced admin dashboards with data visualization
      return MODEL_CONFIGS['claude-opus'] || MODEL_CONFIGS['gpt-4o'];

    case 'ecommerce':
      // For sophisticated e-commerce platforms
      return MODEL_CONFIGS['claude-opus'] || MODEL_CONFIGS['gpt-4o'];

    case 'saas':
      // For complex SaaS applications with multiple features
      return MODEL_CONFIGS['claude-opus'] || MODEL_CONFIGS['gpt-4o'];

    default:
      return MODEL_CONFIGS[envPrefs.preferredModel] || MODEL_CONFIGS['claude-opus'];
  }
}

// Legacy function for backward compatibility
export function selectModelForNextJSTask(taskType: 'component' | 'page' | 'api' | 'fullstack' | 'architecture'): ModelConfig {
  return selectModelForAdvancedWebApp(taskType);
}
